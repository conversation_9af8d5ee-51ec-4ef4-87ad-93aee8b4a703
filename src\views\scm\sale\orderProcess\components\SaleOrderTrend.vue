<template>
  <el-card shadow="hover" class="sale-trend-card" style="height: 23rem;">
    <template #header>
      <div class="flex flex-row items-center justify-between">
        <div style="font-size: 1.0rem; font-weight: bold; margin-right: 1rem;">销售订单趋势</div>
        <el-radio-group v-model="timeRange" @change="initChart">
          <el-radio-button label="week">周</el-radio-button>
          <el-radio-button label="month">月</el-radio-button>
          <el-radio-button label="year">年</el-radio-button>
        </el-radio-group>
      </div>
    </template>
    <div ref="chartRef" class="chart-container"></div>
  </el-card>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue'
import * as echarts from 'echarts'
import { useResizeObserver } from '@vueuse/core'

const chartRef = ref<HTMLElement>()
let chart: echarts.ECharts | null = null
const timeRange = ref('week') // 默认按周显示

// 获取销售趋势数据
const fetchSaleTrendData = async () => {
  try {
    // 添加示例数据
    const sampleData = {
      "week": [
        {"date": "周一", "newCount": 20, "completedCount": 15},
        {"date": "周二", "newCount": 30, "completedCount": 20},
        {"date": "周三", "newCount": 25, "completedCount": 22},
        {"date": "周四", "newCount": 35, "completedCount": 30},
        {"date": "周五", "newCount": 40, "completedCount": 35},
        {"date": "周六", "newCount": 30, "completedCount": 25},
        {"date": "周日", "newCount": 25, "completedCount": 20}
      ],
      "month": [
        {"date": "第1周", "newCount": 120, "completedCount": 100},
        {"date": "第2周", "newCount": 150, "completedCount": 130},
        {"date": "第3周", "newCount": 130, "completedCount": 110},
        {"date": "第4周", "newCount": 160, "completedCount": 140}
      ],
      "year": [
        {"date": "第一季度", "newCount": 500, "completedCount": 450},
        {"date": "第二季度", "newCount": 600, "completedCount": 550},
        {"date": "第三季度", "newCount": 550, "completedCount": 500},
        {"date": "第四季度", "newCount": 650, "completedCount": 600}
      ]
    };
    
    // TODO: 实际项目中可以替换为真实的 API 调用
    // const res = await OrderProcessApi.getSaleTrendData({ timeRange: timeRange.value });
    // return res.data || sampleData[timeRange.value];
    return sampleData[timeRange.value];
  } catch (error) {
    console.error('获取销售趋势数据失败:', error);
    // 使用示例数据
    const sampleData = {
      "week": [
        {"date": "周一", "newCount": 20, "completedCount": 15},
        {"date": "周二", "newCount": 30, "completedCount": 20},
        {"date": "周三", "newCount": 25, "completedCount": 22},
        {"date": "周四", "newCount": 35, "completedCount": 30},
        {"date": "周五", "newCount": 40, "completedCount": 35},
        {"date": "周六", "newCount": 30, "completedCount": 25},
        {"date": "周日", "newCount": 25, "completedCount": 20}
      ],
      "month": [
        {"date": "第1周", "newCount": 120, "completedCount": 100},
        {"date": "第2周", "newCount": 150, "completedCount": 130},
        {"date": "第3周", "newCount": 130, "completedCount": 110},
        {"date": "第4周", "newCount": 160, "completedCount": 140}
      ],
      "year": [
        {"date": "第一季度", "newCount": 500, "completedCount": 450},
        {"date": "第二季度", "newCount": 600, "completedCount": 550},
        {"date": "第三季度", "newCount": 550, "completedCount": 500},
        {"date": "第四季度", "newCount": 650, "completedCount": 600}
      ]
    };
    return sampleData[timeRange.value];
  }
}

// 初始化图表
const initChart = async () => {
  if (!chartRef.value) return

  chart = echarts.init(chartRef.value)

  // 获取数据
  const trendData = await fetchSaleTrendData()
  const dates = trendData.map(item => item.date)
  const newOrders = trendData.map(item => item.newCount)
  const completedOrders = trendData.map(item => item.completedCount)

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985'
        }
      }
    },
    legend: {
      data: ['新增订单', '完成订单'],
      top: 10,
      left: 'center'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      top: '20%',
      containLabel: true
    },
    xAxis: [
      {
        type: 'category',
        boundaryGap: false,
        data: dates,
        axisLabel: {
          rotate: 30
        }
      }
    ],
    yAxis: [
      {
        type: 'value',

        axisLine: {
          show: true
        },
        splitLine: {
          show: true
        }
      }
    ],
    graphic: {
      // 时间范围选择器代码保持不变
    },
    series: [
      {
        name: '新增订单',
        type: 'line',
        smooth: true,
        lineStyle: {
          width: 3,
          color: '#3b82f6'
        },
        showSymbol: false,
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(59, 130, 246, 0.8)' },
            { offset: 1, color: 'rgba(59, 130, 246, 0.1)' }
          ]),
          shadowColor: 'rgba(0, 0, 0, 0.1)',
          shadowBlur: 10
        },
        emphasis: {
          focus: 'series'
        },
        data: newOrders
      },
      {
        name: '完成订单',
        type: 'line',
        smooth: true,
        lineStyle: {
          width: 3,
          color: '#10b981'
        },
        showSymbol: false,
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(16, 185, 129, 0.8)' },
            { offset: 1, color: 'rgba(16, 185, 129, 0.1)' }
          ]),
          shadowColor: 'rgba(0, 0, 0, 0.1)',
          shadowBlur: 10
        },
        emphasis: {
          focus: 'series'
        },
        data: completedOrders
      }
    ]
  }

  chart.setOption(option)
}

// 响应式调整图表大小
useResizeObserver(chartRef, () => {
  chart?.resize()
})

onMounted(() => {
  initChart()
})

onBeforeUnmount(() => {
  chart?.dispose()
})
</script>

<style scoped lang="scss">
.sale-trend-card {
  margin-bottom: 16px;
  border-radius: 12px;
}

.chart-container {
  width: 100%;
  height: 20rem;
}
</style>
